/**
 * 主程序入口
 */

// 引入组件
import CPSChart from "./src/js/components/CPSChart.js";
import InfoDisplay from "./src/js/components/InfoDisplay.js";
import SectionTable from "./src/js/components/SectionTable.js";

/**
 * 设置三个iframe的地址
 * 从环境变量中获取URL并设置到对应的iframe
 */
function setupIframeUrls() {
  // 获取环境变量中的URL
  // const mainUrl = import.meta.env.VITE_CHART_BALANCE;
  // const mainUrl = import.meta.env.VITE_CHART_WIND;
  // const solarUrl = import.meta.env.VITE_CHART_SOLAR;

  const productionUrl =
    "http://26.47.29.11:8080/clouddddp/#/balanceNewTable?userld=3847091120e111e88818fa163e2609bc";
  const mainUrl = import.meta.env.DEV ? "./test/index.html" : productionUrl;

  // // 获取iframe元素
  const balanceIframe = document.querySelector(".balance-chart-layout iframe");
  const windIframe = document.querySelector(".wind-chart-layout iframe");
  const solarIframe = document.querySelector(".solar-chat-layout iframe");

  if (!mainUrl) return;

  // 设置平衡图表iframe的src属性
  if (balanceIframe) {
    balanceIframe.src = mainUrl;
    console.log("平衡图表iframe地址已设置:", mainUrl);
  } else {
    console.warn("平衡图表iframe未找到", {
      iframe: !!balanceIframe,
      url: mainUrl,
    });
  }

  // 设置风电图表iframe的src属性
  if (windIframe) {
    windIframe.src = mainUrl;
    console.log("风电图表iframe地址已设置:", mainUrl);
  } else {
    console.warn("风电图表iframe未找到", {
      iframe: !!windIframe,
      url: mainUrl,
    });
  }

  // 设置光伏图表iframe的src属性
  if (solarIframe) {
    solarIframe.src = mainUrl;
    console.log("光伏图表iframe地址已设置:", mainUrl);
  } else {
    console.warn("光伏图表iframe未找到", {
      iframe: !!solarIframe,
      url: mainUrl,
    });
  }

  // if (mainIframe && mainUrl) {
  //   mainIframe.src = mainUrl;
  //   console.log("主图表iframe地址已设置:", mainUrl);
  // } else {
  //   console.warn("主图表iframe未找到", {
  //     iframe: !!mainIframe,
  //     url: mainUrl,
  //   });
  // }
}

// 页面加载完成后初始化所有图表
document.addEventListener("DOMContentLoaded", () => {
  // 设置三个iframe的地址
  setupIframeUrls();

  // 初始化平衡曲线图表
  // components.balanceChart = new BalanceChart("balance-chart", data.balanceData);

  // 初始化风电消纳曲线图表
  // components.windChart = new WindChart("wind-chart", data.windData);

  // 初始化光伏消纳曲线图表
  // components.solarChart = new SolarChart("solar-chart", data.solarData);

  // 初始化断面监视表格
  new SectionTable("section-table");

  // 初始化CPS曲线图表（不传入静态数据，让组件自动从API获取并启动定时刷新）
  new CPSChart("cps-chart");

  // 初始化信息显示（不传入静态数据，让组件自动从API获取）
  new InfoDisplay("info-container");

  console.log("CPS实时数据管理器已初始化");
});
